"""
IAF-FBO基准实验运行脚本
10维，18客户端，110次评估（50+60），LCB，1轮
"""

import numpy as np
import time
from core import IAF_FBO
from main_iaf_fbo import save_results, save_run_csv, print_final_statistics

def run_benchmark_experiment():
    """运行基准实验"""
    print("IAF-FBO: 联邦贝叶斯多任务优化")
    print("基准实验配置:")
    print("- 维度: 10")
    print("- 客户端数: 18")
    print("- 初始样本: 50")
    print("- 优化轮数: 60")
    print("- 总评估次数: 110")
    print("- 获取函数: LCB (ucb_flag=2)")
    print("- 实验轮数: 1")
    print("=" * 60)
    
    # 设置随机种子确保可重现性
    np.random.seed(42)
    
    # 实验参数
    client_ids = list(range(1, 19))  # 18个客户端
    dimension = 10  # 10维
    n_initial = 50  # 初始样本数
    max_fe = 60  # 额外优化轮数
    ucb_flag = 2  # LCB获取函数
    n_clusters = 6  # 聚类数
    privacy_noise = 0.0  # 无隐私噪声
    transfer_flag = True  # 启用迁移学习
    
    # CSO优化器参数
    pop_size = 100
    max_iter = 100
    phi = 0.1
    
    print(f"开始实验...")
    print("-" * 40)
    
    start_time = time.time()
    
    # 初始化IAF-FBO算法（使用并行加速）
    iaf_fbo = IAF_FBO(
        client_ids=client_ids,
        dimension=dimension,
        n_initial=n_initial,
        max_fe=max_fe,
        ucb_flag=ucb_flag,
        n_clusters=n_clusters,
        privacy_noise=privacy_noise,
        transfer_flag=transfer_flag,
        pop_size=pop_size,
        max_iter=max_iter,
        phi=phi,
        n_jobs=None  # 自动并行
    )
    
    # 运行优化
    print("正在运行IAF-FBO优化...")
    results = iaf_fbo.run(n_runs=1, verbose=True)
    
    end_time = time.time()
    run_time = end_time - start_time
    
    # 构建结果
    run_result = {
        'run_id': 1,
        'results': results[0],
        'runtime': run_time,
        'evaluation_history': iaf_fbo.evaluation_history,
        'parameters': {
            'dimension': dimension,
            'n_initial': n_initial,
            'max_fe': max_fe,
            'ucb_flag': ucb_flag,
            'n_clusters': n_clusters,
            'privacy_noise': privacy_noise,
            'transfer_flag': transfer_flag,
            'pop_size': pop_size,
            'max_iter': max_iter,
            'phi': phi
        }
    }
    
    print(f"\n实验完成!")
    print(f"总运行时间: {run_time:.2f} 秒 ({run_time/60:.1f} 分钟)")
    print("-" * 40)
    
    # 打印每个客户端的最优结果
    print("各客户端最优结果:")
    best_objectives = []
    for client_id in client_ids:
        best_obj = results[0][client_id]['best_y']
        best_objectives.append(best_obj)
        print(f"  客户端 {client_id:2d}: {best_obj:.6f}")
    
    avg_best = np.mean(best_objectives)
    std_best = np.std(best_objectives)
    print(f"\n统计结果:")
    print(f"  平均最优值: {avg_best:.6f}")
    print(f"  标准差:     {std_best:.6f}")
    print(f"  最好结果:   {np.min(best_objectives):.6f}")
    print(f"  最差结果:   {np.max(best_objectives):.6f}")
    
    # 保存结果
    print("\n保存实验结果...")
    
    # 保存CSV格式结果（每个客户端110次评估值）
    save_run_csv(run_result, dimension, max_fe, ucb_flag, n_clusters, transfer_flag)
    
    # 保存完整结果
    all_results = [run_result]
    save_results(all_results, dimension, max_fe, ucb_flag, n_clusters, transfer_flag)
    
    # 打印最终统计
    print_final_statistics(all_results)
    
    print("\n实验结果已保存!")
    print("文件位置:")
    print("- CSV结果: results/")
    print("- 完整结果: results/")
    
    return run_result

def verify_evaluation_count(run_result):
    """验证评估次数是否正确"""
    print("\n验证评估次数...")
    
    expected_total = 110  # 50初始 + 60优化
    
    for client_id in range(1, 19):
        if client_id in run_result['results']:
            client_result = run_result['results'][client_id]
            actual_count = len(client_result['y'])
            
            if actual_count != expected_total:
                print(f"警告: 客户端 {client_id} 评估次数不正确!")
                print(f"  期望: {expected_total}, 实际: {actual_count}")
            else:
                print(f"客户端 {client_id}: {actual_count} 次评估 ✓")
    
    # 验证评估历史记录
    history_count = len(run_result['evaluation_history'])
    expected_history = expected_total  # 每次评估都应该记录
    
    print(f"\n评估历史记录: {history_count} 条")
    if history_count == expected_history:
        print("评估历史记录数量正确 ✓")
    else:
        print(f"警告: 评估历史记录数量不正确! 期望: {expected_history}, 实际: {history_count}")

if __name__ == "__main__":
    print("开始IAF-FBO基准实验...")
    
    try:
        # 运行基准实验
        result = run_benchmark_experiment()
        
        # 验证结果
        verify_evaluation_count(result)
        
        print("\n基准实验成功完成!")
        
    except Exception as e:
        print(f"实验过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
