"""
Test script for IAF-FBO algorithm
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from core import IAF_FBO


def test_simple_case():
    """Test with a simple case (fewer clients and iterations)"""
    print("Testing IAF-FBO with simple case...")
    
    # Simple test configuration
    client_ids = [1, 2, 3, 6]  # Test with 4 clients
    dimension = 10  # Smaller dimension
    n_initial = 20  # Fewer initial samples
    max_fe = 10    # Fewer iterations
    
    # Initialize algorithm
    iaf_fbo = IAF_FBO(
        client_ids=client_ids,
        dimension=dimension,
        n_initial=n_initial,
        max_fe=max_fe,
        ucb_flag=2,  # LCB
        n_clusters=2,
        transfer_flag=True,
        pop_size=50,
        max_iter=20
    )
    
    # Run optimization
    start_time = time.time()
    results = iaf_fbo.run(n_runs=1, verbose=True)
    end_time = time.time()
    
    print(f"\nOptimization completed in {end_time - start_time:.2f} seconds")
    
    # Print results
    print("\nFinal Results:")
    for client_id in client_ids:
        result = results[0][client_id]
        print(f"Client {client_id}: Best objective = {result['best_y']:.6f}")
    
    return results


def test_full_case():
    """Test with full configuration similar to MATLAB"""
    print("Testing IAF-FBO with full configuration...")
    
    # Full test configuration
    client_ids = list(range(1, 19))  # All 18 clients
    dimension = 50
    n_initial = 50
    max_fe = 60
    
    # Initialize algorithm
    iaf_fbo = IAF_FBO(
        client_ids=client_ids,
        dimension=dimension,
        n_initial=n_initial,
        max_fe=max_fe,
        ucb_flag=2,  # LCB
        n_clusters=6,
        transfer_flag=True,
        pop_size=100,
        max_iter=100
    )
    
    # Run optimization
    start_time = time.time()
    results = iaf_fbo.run(n_runs=1, verbose=True)
    end_time = time.time()
    
    print(f"\nOptimization completed in {end_time - start_time:.2f} seconds")
    
    # Print results
    print("\nFinal Results:")
    for client_id in client_ids:
        result = results[0][client_id]
        print(f"Client {client_id}: Best objective = {result['best_y']:.6f}")
    
    return results


def plot_convergence(results, client_ids=None):
    """Plot convergence curves"""
    if client_ids is None:
        client_ids = list(results[0].keys())[:6]  # Plot first 6 clients
    
    plt.figure(figsize=(12, 8))
    
    for i, client_id in enumerate(client_ids):
        result = results[0][client_id]
        y_values = result['y']
        
        # Calculate cumulative minimum
        cum_min = np.minimum.accumulate(y_values)
        
        plt.subplot(2, 3, i + 1)
        plt.plot(cum_min, 'b-', linewidth=2)
        plt.title(f'Client {client_id}')
        plt.xlabel('Function Evaluations')
        plt.ylabel('Best Objective Value')
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('convergence_curves.png', dpi=300, bbox_inches='tight')
    plt.show()


def compare_acquisition_functions():
    """Compare different acquisition functions"""
    print("Comparing acquisition functions...")
    
    client_ids = [1, 2, 3]
    dimension = 10
    n_initial = 20
    max_fe = 15
    
    af_names = {0: 'EI', 1: 'UCB', 2: 'LCB'}
    results_by_af = {}
    
    for ucb_flag in [0, 1, 2]:
        print(f"\nTesting with {af_names[ucb_flag]} acquisition function...")
        
        iaf_fbo = IAF_FBO(
            client_ids=client_ids,
            dimension=dimension,
            n_initial=n_initial,
            max_fe=max_fe,
            ucb_flag=ucb_flag,
            n_clusters=2,
            transfer_flag=True,
            pop_size=30,
            max_iter=20
        )
        
        results = iaf_fbo.run(n_runs=1, verbose=False)
        results_by_af[ucb_flag] = results[0]
        
        # Print final results
        for client_id in client_ids:
            result = results[0][client_id]
            print(f"  Client {client_id}: {result['best_y']:.6f}")
    
    return results_by_af


def test_privacy_noise():
    """Test effect of privacy noise"""
    print("Testing privacy noise effect...")
    
    client_ids = [1, 2]
    dimension = 10
    n_initial = 20
    max_fe = 10
    
    noise_levels = [0.0, 0.1, 0.5]
    results_by_noise = {}
    
    for noise_level in noise_levels:
        print(f"\nTesting with noise level {noise_level}...")
        
        iaf_fbo = IAF_FBO(
            client_ids=client_ids,
            dimension=dimension,
            n_initial=n_initial,
            max_fe=max_fe,
            ucb_flag=2,
            n_clusters=2,
            privacy_noise=noise_level,
            transfer_flag=True,
            pop_size=30,
            max_iter=20
        )
        
        results = iaf_fbo.run(n_runs=1, verbose=False)
        results_by_noise[noise_level] = results[0]
        
        # Print final results
        for client_id in client_ids:
            result = results[0][client_id]
            print(f"  Client {client_id}: {result['best_y']:.6f}")
    
    return results_by_noise


if __name__ == "__main__":
    print("IAF-FBO Python Implementation Test")
    print("=" * 50)
    
    try:
        # Test 1: Simple case
        print("\n1. Simple Case Test")
        print("-" * 30)
        simple_results = test_simple_case()
        
        # Test 2: Compare acquisition functions
        print("\n2. Acquisition Function Comparison")
        print("-" * 30)
        af_results = compare_acquisition_functions()
        
        # Test 3: Privacy noise effect
        print("\n3. Privacy Noise Effect")
        print("-" * 30)
        noise_results = test_privacy_noise()
        
        # Plot convergence for simple case
        print("\n4. Plotting Convergence Curves")
        print("-" * 30)
        plot_convergence(simple_results)
        
        print("\nAll tests completed successfully!")
        
        # Uncomment to run full test (takes longer)
        # print("\n5. Full Configuration Test")
        # print("-" * 30)
        # full_results = test_full_case()
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
