"""
性能比较测试：顺序 vs 并行执行
"""

import time
import multiprocessing as mp
from core import IAF_FBO

def test_sequential_vs_parallel():
    """比较顺序执行与并行执行的性能"""
    print("IAF-FBO 性能比较测试")
    print("=" * 50)
    print(f"系统CPU核心数: {mp.cpu_count()}")
    print()
    
    # 测试配置
    client_ids = [1, 2, 3, 4, 5, 6]  # 6个客户端
    dimension = 10
    n_initial = 15
    max_fe = 8
    
    print(f"测试配置:")
    print(f"  客户端数: {len(client_ids)}")
    print(f"  维度: {dimension}")
    print(f"  初始样本: {n_initial}")
    print(f"  优化轮数: {max_fe}")
    print()
    
    # 顺序执行测试
    print("1. 顺序执行测试...")
    start_time = time.time()
    
    iaf_fbo_seq = IAF_FBO(
        client_ids=client_ids,
        dimension=dimension,
        n_initial=n_initial,
        max_fe=max_fe,
        ucb_flag=2,
        n_clusters=3,
        pop_size=40,
        max_iter=25,
        n_jobs=1  # 顺序执行
    )
    
    results_seq = iaf_fbo_seq.run(n_runs=1, verbose=False)
    seq_time = time.time() - start_time
    
    print(f"   顺序执行完成，用时: {seq_time:.2f} 秒")
    
    # 并行执行测试
    print("2. 并行执行测试...")
    start_time = time.time()
    
    iaf_fbo_par = IAF_FBO(
        client_ids=client_ids,
        dimension=dimension,
        n_initial=n_initial,
        max_fe=max_fe,
        ucb_flag=2,
        n_clusters=3,
        pop_size=40,
        max_iter=25,
        n_jobs=None  # 自动并行
    )
    
    results_par = iaf_fbo_par.run(n_runs=1, verbose=False)
    par_time = time.time() - start_time
    
    print(f"   并行执行完成，用时: {par_time:.2f} 秒")
    
    # 计算加速比
    speedup = seq_time / par_time if par_time > 0 else 0
    
    print()
    print("=" * 50)
    print("性能比较结果:")
    print("=" * 50)
    print(f"顺序执行时间:     {seq_time:.2f} 秒")
    print(f"并行执行时间:     {par_time:.2f} 秒")
    print(f"加速比:          {speedup:.2f}x")
    print(f"时间节省:        {((seq_time - par_time) / seq_time * 100):.1f}%")
    
    # 验证结果质量
    print()
    print("结果质量验证:")
    print("-" * 30)
    
    seq_results = [results_seq[0][cid]['best_y'] for cid in client_ids]
    par_results = [results_par[0][cid]['best_y'] for cid in client_ids]
    
    avg_seq = sum(seq_results) / len(seq_results)
    avg_par = sum(par_results) / len(par_results)
    
    print(f"顺序执行平均最优值: {avg_seq:.6f}")
    print(f"并行执行平均最优值: {avg_par:.6f}")
    print(f"结果差异:          {abs(avg_seq - avg_par):.6f}")
    
    print()
    print("各客户端结果对比:")
    print(f"{'客户端':<8} {'顺序执行':<12} {'并行执行':<12} {'差异':<10}")
    print("-" * 45)
    for i, cid in enumerate(client_ids):
        diff = abs(seq_results[i] - par_results[i])
        print(f"{cid:<8} {seq_results[i]:<12.6f} {par_results[i]:<12.6f} {diff:<10.6f}")
    
    return {
        'seq_time': seq_time,
        'par_time': par_time,
        'speedup': speedup,
        'seq_results': seq_results,
        'par_results': par_results
    }

def test_different_client_numbers():
    """测试不同客户端数量的并行效果"""
    print("\n不同客户端数量的并行效果测试")
    print("=" * 40)
    
    client_configs = [
        {'clients': [1, 2, 3], 'name': '3客户端'},
        {'clients': [1, 2, 3, 4, 5, 6], 'name': '6客户端'},
        {'clients': list(range(1, 10)), 'name': '9客户端'},
    ]
    
    results = {}
    
    for config in client_configs:
        print(f"\n测试 {config['name']}...")
        
        # 顺序执行
        start_time = time.time()
        iaf_fbo_seq = IAF_FBO(
            client_ids=config['clients'],
            dimension=10,
            n_initial=12,
            max_fe=6,
            ucb_flag=2,
            n_clusters=min(3, len(config['clients'])//2),
            pop_size=30,
            max_iter=20,
            n_jobs=1
        )
        iaf_fbo_seq.run(n_runs=1, verbose=False)
        seq_time = time.time() - start_time
        
        # 并行执行
        start_time = time.time()
        iaf_fbo_par = IAF_FBO(
            client_ids=config['clients'],
            dimension=10,
            n_initial=12,
            max_fe=6,
            ucb_flag=2,
            n_clusters=min(3, len(config['clients'])//2),
            pop_size=30,
            max_iter=20,
            n_jobs=None
        )
        iaf_fbo_par.run(n_runs=1, verbose=False)
        par_time = time.time() - start_time
        
        speedup = seq_time / par_time if par_time > 0 else 0
        results[config['name']] = {
            'clients': len(config['clients']),
            'seq_time': seq_time,
            'par_time': par_time,
            'speedup': speedup
        }
        
        print(f"  顺序: {seq_time:.2f}s, 并行: {par_time:.2f}s, 加速比: {speedup:.2f}x")
    
    print("\n客户端数量 vs 加速比总结:")
    print(f"{'配置':<10} {'客户端数':<8} {'顺序(s)':<10} {'并行(s)':<10} {'加速比':<8}")
    print("-" * 50)
    for name, result in results.items():
        print(f"{name:<10} {result['clients']:<8} {result['seq_time']:<10.2f} "
              f"{result['par_time']:<10.2f} {result['speedup']:<8.2f}")
    
    return results

if __name__ == "__main__":
    print("开始IAF-FBO性能比较测试...")
    
    try:
        # 主要性能比较
        main_results = test_sequential_vs_parallel()
        
        # 不同客户端数量测试
        client_results = test_different_client_numbers()
        
        print("\n" + "=" * 50)
        print("测试总结")
        print("=" * 50)
        print(f"主测试加速比: {main_results['speedup']:.2f}x")
        print("建议:")
        if main_results['speedup'] > 2.0:
            print("- 并行化效果显著，建议在实际实验中使用并行模式")
        elif main_results['speedup'] > 1.5:
            print("- 并行化有一定效果，可以根据需要选择使用")
        else:
            print("- 并行化效果有限，可能受到系统资源限制")
        
        print("- 对于大规模实验（≥6个客户端），建议使用并行模式")
        print("- 对于调试和小规模测试，可以使用顺序模式")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
