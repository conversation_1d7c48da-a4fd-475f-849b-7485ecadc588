"""
快速并行测试脚本
"""

import multiprocessing as mp
import time
from core import IAF_FBO

def main():
    print(f'系统CPU核心数: {mp.cpu_count()}')
    print('测试并行化IAF-FBO...')
    
    start_time = time.time()
    
    # 小规模测试
    iaf_fbo = IAF_FBO(
        client_ids=[1, 2, 3, 4],
        dimension=10,
        n_initial=10,
        max_fe=5,
        ucb_flag=2,
        n_clusters=2,
        pop_size=20,
        max_iter=10,
        n_jobs=None  # 自动并行
    )
    
    results = iaf_fbo.run(n_runs=1, verbose=False)
    end_time = time.time()
    
    print(f'测试完成，用时: {end_time - start_time:.2f} 秒')
    print('结果:')
    for client_id, result in results[0].items():
        print(f'  客户端 {client_id}: {result["best_y"]:.6f}')
    
    print('\n并行化测试成功!')

if __name__ == "__main__":
    main()
