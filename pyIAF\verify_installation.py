"""
Verification script to check if the IAF-FBO implementation is working correctly
"""

import sys
import numpy as np

def check_dependencies():
    """Check if all required dependencies are available"""
    print("Checking dependencies...")
    
    required_packages = [
        'numpy', 'scipy', 'sklearn', 'torch', 'matplotlib', 'pandas'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            elif package == 'torch':
                import torch
            elif package == 'matplotlib':
                import matplotlib
            elif package == 'pandas':
                import pandas
            elif package == 'numpy':
                import numpy
            elif package == 'scipy':
                import scipy
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ✗ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {missing_packages}")
        print("Please install them using: pip install " + " ".join(missing_packages))
        return False
    else:
        print("All dependencies are available!")
        return True


def check_modules():
    """Check if all IAF-FBO modules can be imported"""
    print("\nChecking IAF-FBO modules...")
    
    modules = [
        'utils', 'test_functions', 'gaussian_process', 
        'neural_classifier', 'cso_optimizer', 'core'
    ]
    
    failed_imports = []
    
    for module in modules:
        try:
            exec(f"from {module} import *")
            print(f"  ✓ {module}")
        except Exception as e:
            print(f"  ✗ {module} (error: {str(e)[:50]}...)")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\nFailed to import: {failed_imports}")
        return False
    else:
        print("All modules imported successfully!")
        return True


def test_basic_functionality():
    """Test basic functionality of key components"""
    print("\nTesting basic functionality...")
    
    try:
        # Test utility functions
        from utils import lhs_classic, acquisition_function, onehot_conv
        
        # Test LHS
        samples = lhs_classic(10, 5)
        assert samples.shape == (10, 5), "LHS sampling failed"
        print("  ✓ LHS sampling")
        
        # Test acquisition function
        af_vals = acquisition_function([1, 2, 3], [0.1, 0.2, 0.3], [0.5, 1.5, 2.5], 2)
        assert len(af_vals) == 3, "Acquisition function failed"
        print("  ✓ Acquisition function")
        
        # Test one-hot conversion
        labels = np.array([1, -1, 0])
        onehot = onehot_conv(labels, mode=1)
        assert onehot.shape == (3, 2), "One-hot conversion failed"
        print("  ✓ One-hot conversion")
        
        # Test test functions
        from test_functions import choose_problem
        func, bounds, rot_mat, opt_point = choose_problem(1, 5)
        test_x = np.random.uniform(bounds[0], bounds[1], (3, 5))
        test_y = func(test_x)
        assert len(test_y) == 3, "Test function evaluation failed"
        print("  ✓ Test functions")
        
        # Test Gaussian Process
        from gaussian_process import SimpleGP
        gp = SimpleGP()
        X_train = np.random.randn(10, 3)
        y_train = np.random.randn(10)
        gp.fit(X_train, y_train)
        X_test = np.random.randn(5, 3)
        pred_mean, pred_var = gp.predict(X_test)
        assert len(pred_mean) == 5, "GP prediction failed"
        print("  ✓ Gaussian Process")
        
        # Test Neural Classifier
        from neural_classifier import PairwiseClassifier
        import torch
        classifier = PairwiseClassifier(input_dim=10)
        test_input = torch.randn(5, 10)
        output = classifier(test_input)
        assert output.shape == (5, 2), "Neural classifier failed"
        print("  ✓ Neural Classifier")
        
        print("All basic functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"  ✗ Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_minimal_optimization():
    """Test a minimal optimization run"""
    print("\nTesting minimal optimization...")
    
    try:
        from core import IAF_FBO
        
        # Very minimal configuration
        iaf_fbo = IAF_FBO(
            client_ids=[1, 2],
            dimension=3,
            n_initial=5,
            max_fe=2,
            pop_size=10,
            max_iter=5
        )
        
        print("  IAF-FBO initialized successfully")
        
        # Run one iteration
        results = iaf_fbo.run(n_runs=1, verbose=False)
        
        assert len(results) == 1, "Results structure incorrect"
        assert len(results[0]) == 2, "Wrong number of clients in results"
        
        for client_id in [1, 2]:
            assert 'best_y' in results[0][client_id], f"Missing best_y for client {client_id}"
            assert 'best_X' in results[0][client_id], f"Missing best_X for client {client_id}"
        
        print("  ✓ Minimal optimization completed successfully!")
        print(f"  Client 1 best: {results[0][1]['best_y']:.6f}")
        print(f"  Client 2 best: {results[0][2]['best_y']:.6f}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Minimal optimization failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main verification function"""
    print("IAF-FBO Installation Verification")
    print("=" * 50)
    
    # Check dependencies
    deps_ok = check_dependencies()
    if not deps_ok:
        print("\n❌ Dependency check failed. Please install missing packages.")
        return False
    
    # Check module imports
    modules_ok = check_modules()
    if not modules_ok:
        print("\n❌ Module import check failed. Please check the implementation.")
        return False
    
    # Test basic functionality
    basic_ok = test_basic_functionality()
    if not basic_ok:
        print("\n❌ Basic functionality test failed.")
        return False
    
    # Test minimal optimization
    opt_ok = test_minimal_optimization()
    if not opt_ok:
        print("\n❌ Minimal optimization test failed.")
        return False
    
    print("\n" + "=" * 50)
    print("✅ All verification tests passed!")
    print("IAF-FBO is ready to use.")
    print("\nNext steps:")
    print("1. Run 'python test_iaf_fbo.py' for comprehensive testing")
    print("2. Run 'python main_iaf_fbo.py' for full experiments")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
