"""
并行性能测试脚本
测试IAF-FBO算法的并行化效果
"""

import time
import numpy as np
import multiprocessing as mp
from core import IAF_FBO


def test_parallel_vs_sequential():
    """测试并行与顺序执行的性能差异"""
    print("IAF-FBO 并行性能测试")
    print("=" * 50)
    
    # 测试配置
    test_configs = [
        {
            'name': '小规模测试',
            'client_ids': [1, 2, 3, 4],
            'dimension': 10,
            'n_initial': 15,
            'max_fe': 8,
            'pop_size': 30,
            'max_iter': 20
        },
        {
            'name': '中等规模测试',
            'client_ids': list(range(1, 9)),  # 8个客户端
            'dimension': 10,
            'n_initial': 20,
            'max_fe': 10,
            'pop_size': 50,
            'max_iter': 30
        },
        {
            'name': '大规模测试',
            'client_ids': list(range(1, 13)),  # 12个客户端
            'dimension': 10,
            'n_initial': 25,
            'max_fe': 12,
            'pop_size': 60,
            'max_iter': 40
        }
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n{config['name']} ({len(config['client_ids'])}个客户端)")
        print("-" * 30)
        
        # 顺序执行
        print("顺序执行...")
        start_time = time.time()
        
        iaf_fbo_seq = IAF_FBO(
            client_ids=config['client_ids'],
            dimension=config['dimension'],
            n_initial=config['n_initial'],
            max_fe=config['max_fe'],
            ucb_flag=2,
            n_clusters=min(3, len(config['client_ids'])//2),
            pop_size=config['pop_size'],
            max_iter=config['max_iter'],
            n_jobs=1  # 顺序执行
        )
        
        results_seq = iaf_fbo_seq.run(n_runs=1, verbose=False)
        seq_time = time.time() - start_time
        
        # 并行执行
        print("并行执行...")
        start_time = time.time()
        
        iaf_fbo_par = IAF_FBO(
            client_ids=config['client_ids'],
            dimension=config['dimension'],
            n_initial=config['n_initial'],
            max_fe=config['max_fe'],
            ucb_flag=2,
            n_clusters=min(3, len(config['client_ids'])//2),
            pop_size=config['pop_size'],
            max_iter=config['max_iter'],
            n_jobs=None  # 自动并行
        )
        
        results_par = iaf_fbo_par.run(n_runs=1, verbose=False)
        par_time = time.time() - start_time
        
        # 计算结果
        speedup = seq_time / par_time if par_time > 0 else 0
        
        # 验证结果质量
        avg_seq = np.mean([results_seq[0][cid]['best_y'] for cid in config['client_ids']])
        avg_par = np.mean([results_par[0][cid]['best_y'] for cid in config['client_ids']])
        quality_diff = abs(avg_seq - avg_par) / abs(avg_seq) * 100
        
        results[config['name']] = {
            'clients': len(config['client_ids']),
            'seq_time': seq_time,
            'par_time': par_time,
            'speedup': speedup,
            'avg_seq': avg_seq,
            'avg_par': avg_par,
            'quality_diff': quality_diff
        }
        
        print(f"  顺序执行时间: {seq_time:.2f} 秒")
        print(f"  并行执行时间: {par_time:.2f} 秒")
        print(f"  加速比: {speedup:.2f}x")
        print(f"  结果质量差异: {quality_diff:.2f}%")
    
    # 打印总结
    print("\n" + "=" * 50)
    print("性能测试总结")
    print("=" * 50)
    print(f"{'测试':<15} {'客户端':<8} {'顺序(s)':<10} {'并行(s)':<10} {'加速比':<8} {'质量差异(%)':<12}")
    print("-" * 70)
    
    for name, result in results.items():
        print(f"{name:<15} {result['clients']:<8} {result['seq_time']:<10.2f} "
              f"{result['par_time']:<10.2f} {result['speedup']:<8.2f} {result['quality_diff']:<12.2f}")
    
    return results


def test_different_parallel_settings():
    """测试不同并行设置的效果"""
    print("\n不同并行设置测试")
    print("=" * 30)
    
    # 固定测试配置
    client_ids = list(range(1, 9))  # 8个客户端
    dimension = 10
    n_initial = 20
    max_fe = 10
    
    # 不同的并行设置
    parallel_settings = [1, 2, 4, None]  # None表示自动
    cpu_count = mp.cpu_count()
    
    print(f"系统CPU核心数: {cpu_count}")
    print(f"测试客户端数: {len(client_ids)}")
    print("-" * 30)
    
    times = {}
    
    for n_jobs in parallel_settings:
        setting_name = f"{n_jobs}核" if n_jobs else "自动"
        print(f"测试 {setting_name}...")
        
        start_time = time.time()
        
        iaf_fbo = IAF_FBO(
            client_ids=client_ids,
            dimension=dimension,
            n_initial=n_initial,
            max_fe=max_fe,
            ucb_flag=2,
            n_clusters=3,
            pop_size=50,
            max_iter=30,
            n_jobs=n_jobs
        )
        
        results = iaf_fbo.run(n_runs=1, verbose=False)
        elapsed_time = time.time() - start_time
        
        times[setting_name] = elapsed_time
        print(f"  用时: {elapsed_time:.2f} 秒")
    
    # 计算相对于顺序执行的加速比
    seq_time = times.get('1核', 0)
    print(f"\n相对于顺序执行的加速比:")
    for setting, time_val in times.items():
        if setting != '1核' and seq_time > 0:
            speedup = seq_time / time_val
            print(f"  {setting}: {speedup:.2f}x")
    
    return times


def test_memory_usage():
    """测试内存使用情况"""
    import psutil
    import os
    
    print("\n内存使用测试")
    print("=" * 20)
    
    process = psutil.Process(os.getpid())
    
    # 基准内存
    baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"基准内存使用: {baseline_memory:.1f} MB")
    
    # 顺序执行
    print("顺序执行内存测试...")
    iaf_fbo_seq = IAF_FBO(
        client_ids=list(range(1, 7)),
        dimension=10,
        n_initial=15,
        max_fe=8,
        ucb_flag=2,
        n_clusters=2,
        pop_size=40,
        max_iter=25,
        n_jobs=1
    )
    
    results_seq = iaf_fbo_seq.run(n_runs=1, verbose=False)
    seq_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"顺序执行内存使用: {seq_memory:.1f} MB")
    
    # 清理
    del iaf_fbo_seq, results_seq
    
    # 并行执行
    print("并行执行内存测试...")
    iaf_fbo_par = IAF_FBO(
        client_ids=list(range(1, 7)),
        dimension=10,
        n_initial=15,
        max_fe=8,
        ucb_flag=2,
        n_clusters=2,
        pop_size=40,
        max_iter=25,
        n_jobs=None
    )
    
    results_par = iaf_fbo_par.run(n_runs=1, verbose=False)
    par_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"并行执行内存使用: {par_memory:.1f} MB")
    
    print(f"内存增长 (顺序): {seq_memory - baseline_memory:.1f} MB")
    print(f"内存增长 (并行): {par_memory - baseline_memory:.1f} MB")


if __name__ == "__main__":
    print("开始IAF-FBO并行性能测试...")
    print(f"系统信息: {mp.cpu_count()} CPU核心")
    
    try:
        # 主要性能测试
        test_parallel_vs_sequential()
        
        # 不同并行设置测试
        test_different_parallel_settings()
        
        # 内存使用测试
        test_memory_usage()
        
        print("\n所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
