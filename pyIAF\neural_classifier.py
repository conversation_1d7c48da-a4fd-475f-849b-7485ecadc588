"""
Neural Network Classifier for IAF-FBO
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.cluster import KMeans
try:
    from .utils import onehot_conv
except ImportError:
    from utils import onehot_conv


class PairwiseClassifier(nn.Module):
    """
    Neural Network for pairwise comparison classification
    """
    
    def __init__(self, input_dim, hidden_dims=None):
        """
        Initialize classifier
        
        Args:
            input_dim: Input dimension (2 * problem_dimension)
            hidden_dims: List of hidden layer dimensions
        """
        super(PairwiseClassifier, self).__init__()
        
        if hidden_dims is None:
            # Default architecture similar to MATLAB patternnet
            hidden_dims = [
                int(np.ceil(input_dim * 1.5)),
                input_dim,
                int(np.ceil(input_dim / 2))
            ]
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.ReLU())
            prev_dim = hidden_dim
        
        # Output layer (2 classes: winner/loser)
        layers.append(nn.Linear(prev_dim, 2))
        layers.append(nn.Softmax(dim=1))
        
        self.network = nn.Sequential(*layers)
        
    def forward(self, x):
        return self.network(x)
    
    def get_weights_vector(self):
        """
        Get flattened weight vector for similarity measurement
        
        Returns:
            Flattened weight vector
        """
        weights = []
        for param in self.parameters():
            weights.append(param.data.flatten())
        return torch.cat(weights).numpy()
    
    def set_weights_from_vector(self, weight_vector):
        """
        Set weights from flattened vector
        
        Args:
            weight_vector: Flattened weight vector
        """
        idx = 0
        for param in self.parameters():
            param_size = param.numel()
            param.data = torch.tensor(
                weight_vector[idx:idx + param_size].reshape(param.shape),
                dtype=param.dtype
            )
            idx += param_size


class ClassifierTrainer:
    """
    Trainer for pairwise classifier
    """
    
    def __init__(self, learning_rate=0.001, epochs=100, batch_size=32):
        """
        Initialize trainer
        
        Args:
            learning_rate: Learning rate for optimizer
            epochs: Number of training epochs
            batch_size: Batch size for training
        """
        self.learning_rate = learning_rate
        self.epochs = epochs
        self.batch_size = batch_size
    
    def train(self, classifier, X_train, y_train, X_test=None, y_test=None, verbose=False):
        """
        Train the classifier
        
        Args:
            classifier: PairwiseClassifier instance
            X_train: Training input data
            y_train: Training labels
            X_test: Test input data (optional)
            y_test: Test labels (optional)
            verbose: Whether to print training progress
        
        Returns:
            Trained classifier
        """
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train)
        y_train_onehot = onehot_conv(y_train, mode=1)
        y_train_tensor = torch.FloatTensor(y_train_onehot)
        
        # Remove second column as in MATLAB code
        y_train_tensor = y_train_tensor[:, [0]]  # Keep only first column
        
        # Create binary labels (0 or 1)
        y_binary = torch.zeros(len(y_train_tensor), 2)
        y_binary[y_train_tensor[:, 0] == 1, 0] = 1  # Class 1
        y_binary[y_train_tensor[:, 0] == 0, 1] = 1  # Class -1
        
        # Setup optimizer and loss
        optimizer = optim.Adam(classifier.parameters(), lr=self.learning_rate)
        criterion = nn.CrossEntropyLoss()
        
        # Training loop
        classifier.train()
        for epoch in range(self.epochs):
            # Shuffle data
            indices = torch.randperm(len(X_train_tensor))
            X_shuffled = X_train_tensor[indices]
            y_shuffled = y_binary[indices]
            
            total_loss = 0
            n_batches = 0
            
            # Mini-batch training
            for i in range(0, len(X_train_tensor), self.batch_size):
                batch_X = X_shuffled[i:i + self.batch_size]
                batch_y = y_shuffled[i:i + self.batch_size]
                
                optimizer.zero_grad()
                
                outputs = classifier(batch_X)
                loss = criterion(outputs, torch.argmax(batch_y, dim=1))
                
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                n_batches += 1
            
            if verbose and (epoch + 1) % 20 == 0:
                avg_loss = total_loss / n_batches
                print(f"Epoch {epoch + 1}/{self.epochs}, Loss: {avg_loss:.4f}")
        
        return classifier
    
    def evaluate(self, classifier, X_test, y_test):
        """
        Evaluate classifier performance
        
        Args:
            classifier: Trained classifier
            X_test: Test input data
            y_test: Test labels
        
        Returns:
            Accuracy
        """
        classifier.eval()
        
        with torch.no_grad():
            X_test_tensor = torch.FloatTensor(X_test)
            outputs = classifier(X_test_tensor)
            predictions = onehot_conv(outputs.numpy(), mode=2)
            
            accuracy = np.mean(predictions == y_test)
        
        return accuracy


def aggregate_classifiers(classifiers, client_ids):
    """
    Aggregate classifier weights from similar clients
    
    Args:
        classifiers: List of trained classifiers
        client_ids: List of client IDs to aggregate
    
    Returns:
        Aggregated classifier
    """
    if len(client_ids) == 0:
        return None
    
    # Get reference classifier structure
    ref_classifier = classifiers[client_ids[0]]
    aggregated = PairwiseClassifier(
        input_dim=ref_classifier.network[0].in_features
    )
    
    # Initialize aggregated weights
    aggregated_params = {}
    for name, param in ref_classifier.named_parameters():
        aggregated_params[name] = torch.zeros_like(param)
    
    # Average weights
    for client_id in client_ids:
        classifier = classifiers[client_id]
        for name, param in classifier.named_parameters():
            aggregated_params[name] += param.data
    
    # Divide by number of clients
    for name in aggregated_params:
        aggregated_params[name] /= len(client_ids)
    
    # Set aggregated weights
    for name, param in aggregated.named_parameters():
        param.data = aggregated_params[name]
    
    return aggregated


def measure_classifier_similarity(classifiers, n_clusters=6):
    """
    Measure similarity between classifiers using K-means clustering
    
    Args:
        classifiers: List of trained classifiers
        n_clusters: Number of clusters
    
    Returns:
        Cluster assignments for each classifier
    """
    # Extract weight vectors
    weight_vectors = []
    for classifier in classifiers:
        weight_vector = classifier.get_weights_vector()
        weight_vectors.append(weight_vector)
    
    weight_matrix = np.array(weight_vectors)
    
    # Perform K-means clustering
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    cluster_assignments = kmeans.fit_predict(weight_matrix)
    
    return cluster_assignments


def create_pairwise_dataset(input_data, af_values):
    """
    Create pairwise comparison dataset
    
    Args:
        input_data: Input solutions
        af_values: Acquisition function values
    
    Returns:
        X_pairs, y_pairs: Pairwise input and labels
    """
    n_samples = len(input_data)
    n_pairs = n_samples * (n_samples - 1) // 2
    
    X_pairs = []
    y_pairs = []
    
    for i in range(n_samples):
        for j in range(i + 1, n_samples):
            # Create pair [x_i, x_j]
            pair = np.concatenate([input_data[i], input_data[j]])
            X_pairs.append(pair)
            
            # Create label based on AF values
            if af_values[i] > af_values[j]:
                y_pairs.append(1)
            elif af_values[i] < af_values[j]:
                y_pairs.append(-1)
            else:
                y_pairs.append(0)
    
    return np.array(X_pairs), np.array(y_pairs)
