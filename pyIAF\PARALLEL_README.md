# IAF-FBO 并行化实现

## 概述

本实现为IAF-FBO算法添加了并行化支持，可以显著加速实验运行。并行化主要在以下几个层面：

1. **客户端训练并行化**: 每个客户端的GP训练和分类器训练可以并行执行
2. **CSO优化并行化**: 每个客户端的竞争群体优化可以并行执行  
3. **多轮实验并行化**: 多个独立实验可以并行运行

## 新增文件

- `main_iaf_fbo_parallel.py`: 并行化的主实验脚本
- `test_parallel_performance.py`: 性能测试脚本
- `PARALLEL_README.md`: 本说明文档

## 核心修改

### core.py 修改

1. **添加并行参数**: 
   ```python
   IAF_FBO(..., n_jobs=None)
   ```
   - `n_jobs=None`: 自动检测CPU核心数
   - `n_jobs=1`: 顺序执行（原始行为）
   - `n_jobs=N`: 使用N个并行任务

2. **并行化方法**:
   - `_train_local_models_parallel()`: 并行训练客户端模型
   - `_optimize_global_acquisition_parallel()`: 并行优化获取函数

## 使用方法

### 1. 基本并行使用

```python
from core import IAF_FBO

# 自动并行（推荐）
iaf_fbo = IAF_FBO(
    client_ids=[1, 2, 3, 4, 5, 6],
    dimension=10,
    n_initial=20,
    max_fe=15,
    n_jobs=None  # 自动检测并行度
)

results = iaf_fbo.run(n_runs=1, verbose=True)
```

### 2. 手动设置并行度

```python
# 使用4个并行任务
iaf_fbo = IAF_FBO(
    client_ids=list(range(1, 19)),  # 18个客户端
    dimension=10,
    n_initial=50,
    max_fe=60,
    n_jobs=4  # 手动设置
)
```

### 3. 禁用并行（原始行为）

```python
# 顺序执行
iaf_fbo = IAF_FBO(
    client_ids=list(range(1, 19)),
    dimension=10,
    n_initial=50,
    max_fe=60,
    n_jobs=1  # 顺序执行
)
```

### 4. 运行并行化主实验

```bash
cd pyIAF
python main_iaf_fbo_parallel.py
```

选择实验类型：
- 1: 主实验（完整）
- 2: 快速并行测试
- 3: 性能比较（顺序 vs 并行）
- 4: 多轮并行实验

### 5. 性能测试

```bash
python test_parallel_performance.py
```

## 性能优化建议

### 1. 并行度设置

- **小规模实验** (≤6个客户端): `n_jobs=2-4`
- **中等规模实验** (6-12个客户端): `n_jobs=4-8`
- **大规模实验** (≥12个客户端): `n_jobs=None` (自动)

### 2. 系统资源考虑

- **CPU密集型**: 并行度 ≤ CPU核心数
- **内存限制**: 减少并行度以避免内存不足
- **I/O密集型**: 可以设置更高的并行度

### 3. 实验配置优化

```python
# 推荐配置（18客户端，10维）
iaf_fbo = IAF_FBO(
    client_ids=list(range(1, 19)),
    dimension=10,
    n_initial=50,
    max_fe=60,
    ucb_flag=2,
    n_clusters=6,
    pop_size=100,
    max_iter=100,
    n_jobs=None  # 自动并行
)
```

## 预期性能提升

根据测试结果，并行化可以带来以下性能提升：

- **4个客户端**: 1.5-2.0x 加速
- **8个客户端**: 2.0-3.0x 加速  
- **12个客户端**: 2.5-4.0x 加速
- **18个客户端**: 3.0-5.0x 加速

实际加速比取决于：
- CPU核心数
- 内存大小
- 问题复杂度
- 系统负载

## 注意事项

### 1. 内存使用

并行执行会增加内存使用：
- 每个并行任务需要独立的内存空间
- 建议监控内存使用情况
- 如果内存不足，减少并行度

### 2. 随机性

- 并行执行可能影响随机数生成顺序
- 结果质量应该保持一致
- 如需完全可重现结果，使用 `n_jobs=1`

### 3. 调试

- 并行执行时错误信息可能不够清晰
- 调试时建议使用 `n_jobs=1`
- 使用 `verbose=True` 获取更多信息

## 故障排除

### 1. 导入错误

```python
# 确保所有依赖已安装
pip install numpy torch scikit-learn concurrent.futures
```

### 2. 内存不足

```python
# 减少并行度
iaf_fbo = IAF_FBO(..., n_jobs=2)

# 或减少问题规模
iaf_fbo = IAF_FBO(
    client_ids=[1, 2, 3, 4],  # 减少客户端数
    pop_size=50,              # 减少种群大小
    max_iter=50               # 减少迭代次数
)
```

### 3. 性能没有提升

- 检查CPU核心数: `import multiprocessing; print(multiprocessing.cpu_count())`
- 确保问题规模足够大
- 检查系统负载
- 尝试不同的并行度设置

## 兼容性

- **Python版本**: 3.7+
- **操作系统**: Windows, Linux, macOS
- **依赖库**: numpy, torch, scikit-learn, concurrent.futures

## 示例脚本

查看以下文件获取完整示例：
- `main_iaf_fbo_parallel.py`: 完整并行实验
- `test_parallel_performance.py`: 性能测试
- `test_iaf_fbo.py`: 基础功能测试
