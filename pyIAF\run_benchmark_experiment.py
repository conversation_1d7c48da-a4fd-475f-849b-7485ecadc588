"""
基准函数实验脚本
运行配置：10维，18客户端，110轮(50+60)，niid2，1轮实验
"""

import numpy as np
import time
import pandas as pd
from datetime import datetime
from core import IAF_FBO


def run_benchmark_experiment():
    """
    运行基准函数实验
    配置：10维，18客户端，110轮(50+60)，niid2，1轮
    """
    print("=" * 60)
    print("基准函数实验 - IAF-FBO算法")
    print("配置：10维，18客户端，110轮(50+60)，niid2，1轮")
    print("=" * 60)
    
    # 实验参数设置
    n_runs = 1  # 运行1轮
    client_ids = list(range(1, 19))  # 18个客户端
    dimension = 10  # 10维
    n_initial = 50  # 初始样本数
    max_fe = 60  # 额外函数评估次数 (总共110次 = 50+60)
    ucb_flag = 2  # LCB获取函数
    n_clusters = 6  # 聚类数
    privacy_noise = 0.0  # 无隐私噪声
    transfer_flag = True  # 启用知识迁移
    
    # CSO优化器参数
    pop_size = 100
    max_iter = 100
    phi = 0.1
    
    print(f"实验配置:")
    print(f"  客户端数量: {len(client_ids)}")
    print(f"  问题维度: {dimension}")
    print(f"  初始样本数: {n_initial}")
    print(f"  最大函数评估次数: {max_fe}")
    print(f"  总评估次数: {n_initial + max_fe}")
    print(f"  获取函数: {'LCB' if ucb_flag == 2 else 'UCB' if ucb_flag == 1 else 'EI'}")
    print(f"  聚类数: {n_clusters}")
    print(f"  知识迁移: {transfer_flag}")
    print(f"  数据分布: niid2")
    print(f"  运行轮数: {n_runs}")
    print()
    
    # 存储所有运行结果
    all_results = []
    
    # 开始实验
    for run in range(n_runs):
        print(f"开始运行 {run + 1}/{n_runs}")
        print("-" * 40)
        
        start_time = time.time()
        
        # 初始化IAF-FBO算法
        iaf_fbo = IAF_FBO(
            client_ids=client_ids,
            dimension=dimension,
            n_initial=n_initial,
            max_fe=max_fe,
            ucb_flag=ucb_flag,
            n_clusters=n_clusters,
            privacy_noise=privacy_noise,
            transfer_flag=transfer_flag,
            pop_size=pop_size,
            max_iter=max_iter,
            phi=phi
        )
        
        # 运行优化
        print("开始优化过程...")
        results = iaf_fbo.run(n_runs=1, verbose=True)
        
        end_time = time.time()
        run_time = end_time - start_time
        
        # 存储结果
        run_result = {
            'run_id': run + 1,
            'results': results[0],
            'runtime': run_time,
            'evaluation_history': iaf_fbo.evaluation_history,
            'parameters': {
                'dimension': dimension,
                'n_initial': n_initial,
                'max_fe': max_fe,
                'total_fe': n_initial + max_fe,
                'ucb_flag': ucb_flag,
                'n_clusters': n_clusters,
                'privacy_noise': privacy_noise,
                'transfer_flag': transfer_flag,
                'data_distribution': 'niid2'
            }
        }
        
        all_results.append(run_result)
        
        # 保存CSV文件
        save_evaluation_csv(run_result)
        
        # 打印本轮总结
        print(f"\n运行 {run + 1} 总结:")
        print(f"  运行时间: {run_time:.2f} 秒")
        
        best_objectives = []
        print(f"  各客户端最优值:")
        for client_id in client_ids:
            best_obj = results[0][client_id]['best_y']
            best_objectives.append(best_obj)
            print(f"    客户端 {client_id:2d}: {best_obj:.6f}")
        
        avg_best = np.mean(best_objectives)
        std_best = np.std(best_objectives)
        print(f"  平均最优值: {avg_best:.6f} ± {std_best:.6f}")
        print()
    
    # 保存完整结果
    save_complete_results(all_results)
    
    # 打印最终统计
    print_final_summary(all_results)
    
    return all_results


def save_evaluation_csv(run_result):
    """保存单次运行的评估历史为CSV文件"""
    run_id = run_result['run_id']
    evaluation_history = run_result['evaluation_history']
    params = run_result['parameters']
    
    if not evaluation_history:
        print(f"警告: 运行 {run_id} 没有评估历史")
        return
    
    # 创建文件名（与参考格式一致）
    af_name = {0: 'EI', 1: 'UCB', 2: 'LCB'}[params['ucb_flag']]
    total_fe = params['total_fe']
    n_clients = len(run_result['results'])
    
    filename = (f"run{run_id-1}_DMT_{n_clients}.{n_clients}_"
               f"ktp0.8_FE{total_fe}_{af_name}0.5_niid2_fedavg.csv")
    
    # 准备CSV数据
    csv_data = []
    client_columns = [f"client{i}" for i in range(n_clients)]
    
    # 确保有足够的评估记录（110次）
    expected_evaluations = total_fe
    actual_evaluations = len(evaluation_history)
    
    print(f"  评估历史记录数: {actual_evaluations}/{expected_evaluations}")
    
    for eval_record in evaluation_history:
        row = []
        # 按顺序添加客户端值
        for i in range(n_clients):
            client_id = i + 1  # 客户端ID从1开始
            if client_id in eval_record:
                row.append(eval_record[client_id])
            else:
                row.append(np.nan)  # 缺失值
        
        # 添加时间戳
        row.append(eval_record.get('time', 0.0))
        csv_data.append(row)
    
    # 创建DataFrame
    columns = client_columns + ['time']
    df = pd.DataFrame(csv_data, columns=columns)
    
    # 保存为CSV
    df.to_csv(filename, index=True)
    print(f"  评估历史已保存到: {filename}")


def save_complete_results(all_results):
    """保存完整的实验结果"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    params = all_results[0]['parameters']
    
    # 创建文件名
    filename = (f"benchmark_experiment_D{params['dimension']}_"
               f"C{len(all_results[0]['results'])}_"
               f"FE{params['total_fe']}_niid2_{timestamp}.txt")
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("基准函数实验结果\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"实验参数:\n")
        f.write(f"  客户端数量: {len(all_results[0]['results'])}\n")
        f.write(f"  问题维度: {params['dimension']}\n")
        f.write(f"  初始样本数: {params['n_initial']}\n")
        f.write(f"  最大函数评估: {params['max_fe']}\n")
        f.write(f"  总评估次数: {params['total_fe']}\n")
        f.write(f"  获取函数: {params['ucb_flag']}\n")
        f.write(f"  聚类数: {params['n_clusters']}\n")
        f.write(f"  知识迁移: {params['transfer_flag']}\n")
        f.write(f"  数据分布: {params['data_distribution']}\n")
        f.write(f"  运行轮数: {len(all_results)}\n\n")
        
        # 各客户端统计
        client_ids = sorted(all_results[0]['results'].keys())
        for client_id in client_ids:
            best_values = [run['results'][client_id]['best_y'] for run in all_results]
            f.write(f"客户端 {client_id}:\n")
            f.write(f"  均值: {np.mean(best_values):.6f}\n")
            f.write(f"  标准差: {np.std(best_values):.6f}\n")
            f.write(f"  最小值: {np.min(best_values):.6f}\n")
            f.write(f"  最大值: {np.max(best_values):.6f}\n\n")
    
    print(f"完整结果已保存到: {filename}")


def print_final_summary(all_results):
    """打印最终总结"""
    print("实验完成总结")
    print("=" * 50)
    
    n_runs = len(all_results)
    client_ids = sorted(all_results[0]['results'].keys())
    params = all_results[0]['parameters']
    
    # 运行时间统计
    runtimes = [run['runtime'] for run in all_results]
    print(f"运行时间统计:")
    print(f"  平均时间: {np.mean(runtimes):.2f} ± {np.std(runtimes):.2f} 秒")
    print(f"  总时间: {np.sum(runtimes):.2f} 秒")
    print()
    
    # 各客户端目标函数统计
    print("各客户端最优目标函数值:")
    print("客户端ID | 均值 ± 标准差 | 最小值 | 最大值")
    print("-" * 45)
    
    overall_means = []
    for client_id in client_ids:
        best_values = [run['results'][client_id]['best_y'] for run in all_results]
        mean_val = np.mean(best_values)
        std_val = np.std(best_values)
        min_val = np.min(best_values)
        max_val = np.max(best_values)
        
        overall_means.append(mean_val)
        print(f"{client_id:9d} | {mean_val:6.3f} ± {std_val:5.3f} | {min_val:6.3f} | {max_val:6.3f}")
    
    print("-" * 45)
    print(f"总体平均  | {np.mean(overall_means):6.3f} ± {np.std(overall_means):5.3f}")
    print()
    
    print(f"实验配置总结:")
    print(f"  维度: {params['dimension']}D")
    print(f"  客户端: {len(client_ids)}个")
    print(f"  总评估: {params['total_fe']}次 ({params['n_initial']}+{params['max_fe']})")
    print(f"  数据分布: {params['data_distribution']}")
    print(f"  运行轮数: {n_runs}轮")


if __name__ == "__main__":
    print("开始基准函数实验...")
    results = run_benchmark_experiment()
    print("实验完成！")
