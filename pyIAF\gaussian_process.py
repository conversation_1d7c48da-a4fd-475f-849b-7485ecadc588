"""
Gaussian Process implementation for IAF-FBO
"""

import numpy as np
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, ConstantKernel
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')


class SimpleGP:
    """
    Simple Gaussian Process implementation similar to DACE
    """
    
    def __init__(self, kernel=None, alpha=1e-5, normalize_y=True):
        """
        Initialize Gaussian Process
        
        Args:
            kernel: Kernel function (default: RBF)
            alpha: Regularization parameter
            normalize_y: Whether to normalize target values
        """
        if kernel is None:
            kernel = ConstantKernel(1.0) * RBF(length_scale=1.0)
        
        self.gp = GaussianProcessRegressor(
            kernel=kernel,
            alpha=alpha,
            normalize_y=normalize_y,
            random_state=42
        )
        self.is_fitted = False
        
    def fit(self, X, y):
        """
        Fit Gaussian Process to data
        
        Args:
            X: Input data (n_samples, n_features)
            y: Target values (n_samples,)
        """
        X = np.atleast_2d(X)
        y = np.atleast_1d(y)
        
        # Remove duplicates
        unique_indices = self._get_unique_indices(X)
        X_unique = X[unique_indices]
        y_unique = y[unique_indices]
        
        # Normalize y to [0, 1]
        self.y_min = np.min(y_unique)
        self.y_max = np.max(y_unique)
        
        if self.y_max > self.y_min:
            y_norm = (y_unique - self.y_min) / (self.y_max - self.y_min)
        else:
            y_norm = np.zeros_like(y_unique)
        
        # Fit GP
        self.gp.fit(X_unique, y_norm)
        self.is_fitted = True
        
        return self
    
    def predict(self, X, return_std=True):
        """
        Make predictions
        
        Args:
            X: Input points (n_samples, n_features)
            return_std: Whether to return standard deviation
        
        Returns:
            mean, std (if return_std=True) or just mean
        """
        if not self.is_fitted:
            raise ValueError("GP must be fitted before making predictions")
        
        X = np.atleast_2d(X)
        
        if return_std:
            mean_norm, std_norm = self.gp.predict(X, return_std=True)
            
            # Denormalize mean
            if self.y_max > self.y_min:
                mean = mean_norm * (self.y_max - self.y_min) + self.y_min
                std = std_norm * (self.y_max - self.y_min)
            else:
                mean = np.full_like(mean_norm, self.y_min)
                std = np.zeros_like(std_norm)
            
            return mean, std**2  # Return variance (MSE)
        else:
            mean_norm = self.gp.predict(X, return_std=False)
            
            # Denormalize mean
            if self.y_max > self.y_min:
                mean = mean_norm * (self.y_max - self.y_min) + self.y_min
            else:
                mean = np.full_like(mean_norm, self.y_min)
            
            return mean
    
    def _get_unique_indices(self, X, tol=1e-10):
        """
        Get indices of unique rows in X
        
        Args:
            X: Input array
            tol: Tolerance for uniqueness
        
        Returns:
            Indices of unique rows
        """
        if len(X) == 0:
            return np.array([], dtype=int)
        
        # Use numpy's unique function with tolerance
        # Round to handle floating point precision
        X_rounded = np.round(X / tol) * tol
        _, unique_indices = np.unique(X_rounded, axis=0, return_index=True)
        
        return np.sort(unique_indices)
    
    @property
    def kernel_(self):
        """Get fitted kernel"""
        if self.is_fitted:
            return self.gp.kernel_
        else:
            return self.gp.kernel


class DACELikeGP:
    """
    DACE-like Gaussian Process implementation
    """
    
    def __init__(self, theta_init=None, theta_bounds=None):
        """
        Initialize DACE-like GP
        
        Args:
            theta_init: Initial theta values
            theta_bounds: Bounds for theta optimization
        """
        self.theta_init = theta_init
        self.theta_bounds = theta_bounds
        self.is_fitted = False
        
    def fit(self, X, y):
        """
        Fit GP using DACE-like approach
        
        Args:
            X: Input data
            y: Target values
        """
        X = np.atleast_2d(X)
        y = np.atleast_1d(y)
        
        # Remove duplicates
        unique_indices = self._get_unique_indices(X)
        self.X_train = X[unique_indices]
        self.y_train = y[unique_indices]
        
        # Normalize y
        self.y_min = np.min(self.y_train)
        self.y_max = np.max(self.y_train)
        
        if self.y_max > self.y_min:
            self.y_train_norm = (self.y_train - self.y_min) / (self.y_max - self.y_min)
        else:
            self.y_train_norm = np.zeros_like(self.y_train)
        
        # Initialize theta
        n_features = self.X_train.shape[1]
        if self.theta_init is None:
            self.theta = 5.0 * np.ones(n_features)
        else:
            self.theta = np.array(self.theta_init)
        
        # Optimize theta
        if self.theta_bounds is not None:
            self._optimize_theta()
        
        # Compute correlation matrix and other parameters
        self._compute_parameters()
        
        self.is_fitted = True
        return self
    
    def predict(self, X, return_std=True):
        """
        Make predictions
        
        Args:
            X: Input points
            return_std: Whether to return standard deviation
        
        Returns:
            mean, variance (if return_std=True) or just mean
        """
        if not self.is_fitted:
            raise ValueError("GP must be fitted before making predictions")
        
        X = np.atleast_2d(X)
        n_test = X.shape[0]
        
        # Compute correlation between test and training points
        k_star = self._correlation_matrix(X, self.X_train, self.theta)
        
        # Compute mean prediction
        mean_norm = np.dot(k_star, self.alpha)
        
        # Denormalize
        if self.y_max > self.y_min:
            mean = mean_norm * (self.y_max - self.y_min) + self.y_min
        else:
            mean = np.full(n_test, self.y_min)
        
        if return_std:
            # Compute variance
            k_star_star = self._correlation_matrix(X, X, self.theta)
            
            # Solve for variance
            v = np.linalg.solve(self.L, k_star.T)
            variance_norm = np.diag(k_star_star) - np.sum(v**2, axis=0)
            variance_norm = np.maximum(variance_norm, 0)  # Ensure non-negative
            
            # Scale variance
            if self.y_max > self.y_min:
                variance = variance_norm * (self.y_max - self.y_min)**2
            else:
                variance = np.zeros(n_test)
            
            return mean, variance
        else:
            return mean
    
    def _correlation_matrix(self, X1, X2, theta):
        """
        Compute correlation matrix using Gaussian correlation
        
        Args:
            X1, X2: Input arrays
            theta: Correlation parameters
        
        Returns:
            Correlation matrix
        """
        X1 = np.atleast_2d(X1)
        X2 = np.atleast_2d(X2)
        
        n1, n2 = X1.shape[0], X2.shape[0]
        K = np.zeros((n1, n2))
        
        for i in range(n1):
            for j in range(n2):
                diff = X1[i] - X2[j]
                K[i, j] = np.exp(-np.sum(theta * diff**2))
        
        return K
    
    def _optimize_theta(self):
        """
        Optimize theta parameters
        """
        def objective(theta):
            try:
                K = self._correlation_matrix(self.X_train, self.X_train, theta)
                K += 1e-6 * np.eye(len(K))  # Regularization
                
                L = np.linalg.cholesky(K)
                alpha = np.linalg.solve(L, self.y_train_norm)
                alpha = np.linalg.solve(L.T, alpha)
                
                # Log marginal likelihood (simplified)
                log_likelihood = (-0.5 * np.dot(self.y_train_norm, alpha) - 
                                np.sum(np.log(np.diag(L))))
                
                return -log_likelihood
            except:
                return 1e10
        
        # Optimize
        bounds = self.theta_bounds if self.theta_bounds else [(1e-5, 100)] * len(self.theta)
        
        result = minimize(objective, self.theta, bounds=bounds, method='L-BFGS-B')
        
        if result.success:
            self.theta = result.x
    
    def _compute_parameters(self):
        """
        Compute GP parameters after theta optimization
        """
        # Correlation matrix
        K = self._correlation_matrix(self.X_train, self.X_train, self.theta)
        K += 1e-6 * np.eye(len(K))  # Regularization
        
        # Cholesky decomposition
        self.L = np.linalg.cholesky(K)
        
        # Solve for alpha
        self.alpha = np.linalg.solve(self.L, self.y_train_norm)
        self.alpha = np.linalg.solve(self.L.T, self.alpha)
    
    def _get_unique_indices(self, X, tol=1e-10):
        """Get indices of unique rows"""
        if len(X) == 0:
            return np.array([], dtype=int)
        
        X_rounded = np.round(X / tol) * tol
        _, unique_indices = np.unique(X_rounded, axis=0, return_index=True)
        
        return np.sort(unique_indices)
