"""
Utility functions for IAF-FBO algorithm
"""

import numpy as np
from scipy.stats import norm
from sklearn.model_selection import train_test_split
try:
    from pyDOE2 import lhs
except ImportError:
    try:
        from scipy.stats import qmc
        def lhs(n, samples):
            """Fallback LHS implementation using scipy"""
            sampler = qmc.LatinHypercube(d=n)
            return sampler.random(n=samples)
    except ImportError:
        def lhs(n, samples):
            """Simple fallback LHS implementation"""
            return np.random.rand(samples, n)


def data_process(input_data, output_data, train_ratio=0.75):
    """
    Divide the data into train and test sets in proportion
    
    Args:
        input_data: Input features
        output_data: Output labels
        train_ratio: Ratio of training data (default: 0.75)
    
    Returns:
        train_in, train_out, test_in, test_out
    """
    # Find indices for different classes
    index_0 = np.where(output_data == 0)[0]
    index_p1 = np.where(output_data == 1)[0]
    index_n1 = np.where(output_data == -1)[0]
    
    # Sample training indices for each class
    train_indices = []
    
    if len(index_0) > 0:
        n_train_0 = int(np.ceil(train_ratio * len(index_0)))
        train_0 = np.random.choice(index_0, n_train_0, replace=False)
        train_indices.extend(train_0)
    
    if len(index_p1) > 0:
        n_train_p1 = int(np.ceil(train_ratio * len(index_p1)))
        train_p1 = np.random.choice(index_p1, n_train_p1, replace=False)
        train_indices.extend(train_p1)
    
    if len(index_n1) > 0:
        n_train_n1 = int(np.ceil(train_ratio * len(index_n1)))
        train_n1 = np.random.choice(index_n1, n_train_n1, replace=False)
        train_indices.extend(train_n1)
    
    # Get training data
    train_indices = np.array(train_indices)
    train_in = input_data[train_indices]
    train_out = output_data[train_indices]
    
    # Get test data
    all_indices = np.arange(len(input_data))
    test_indices = np.setdiff1d(all_indices, train_indices)
    test_in = input_data[test_indices]
    test_out = output_data[test_indices]
    
    return train_in, train_out, test_in, test_out


def onehot_conv(labels, mode=1):
    """
    Convert between labels and one-hot encoding
    
    Args:
        labels: Input labels or one-hot vectors
        mode: 1 for label->onehot, 2 for onehot->label
    
    Returns:
        Converted data
    """
    if mode == 1:
        # Convert labels to one-hot
        labels = np.array(labels)
        onehot = np.zeros((len(labels), 2))
        onehot[labels == 1, 0] = 1
        onehot[labels == -1, 1] = 1
        return onehot
    
    elif mode == 2:
        # Convert one-hot to labels
        onehot = np.array(labels)
        max_indices = np.argmax(onehot, axis=1)
        result = np.zeros(len(onehot))
        result[max_indices == 0] = 1
        result[max_indices == 1] = -1
        return result.astype(int)


def acquisition_function(pop_obj, mse, a1_obj, ucb_flag):
    """
    Calculate acquisition function values
    
    Args:
        pop_obj: Predicted objective values
        mse: Mean squared error (uncertainty)
        a1_obj: Historical objective values
        ucb_flag: 0=EI, 1=UCB, 2=LCB
    
    Returns:
        Acquisition function values
    """
    pop_obj = np.array(pop_obj)
    mse = np.array(mse)
    
    if ucb_flag == 1:
        # UCB
        return pop_obj + 2 * np.sqrt(mse)
    
    elif ucb_flag == 0:
        # EI (Expected Improvement)
        yy = pop_obj
        s = np.sqrt(mse)
        fmin = np.min(a1_obj)
        
        improvement = fmin - yy
        
        # Handle zero variance case
        ei = np.zeros_like(yy)
        mask = s > 0
        
        if np.any(mask):
            z = improvement[mask] / s[mask]
            ei[mask] = improvement[mask] * norm.cdf(z) + s[mask] * norm.pdf(z)
        
        # Handle zero variance case
        ei[~mask & (yy < fmin)] = fmin - yy[~mask & (yy < fmin)]
        
        return -ei  # Negative because we want to maximize EI
    
    elif ucb_flag == 2:
        # LCB
        return pop_obj - 2 * np.sqrt(mse)
    
    else:
        raise ValueError(f"Invalid ucb_flag: {ucb_flag}")


def lhs_classic(n_samples, n_dims):
    """
    Latin Hypercube Sampling

    Args:
        n_samples: Number of samples
        n_dims: Number of dimensions

    Returns:
        LHS samples in [0,1]^n_dims
    """
    try:
        # Try with pyDOE2 if available
        return lhs(n_dims, samples=n_samples, criterion='classic')
    except (TypeError, AttributeError):
        # Fallback to simple LHS
        return lhs(n_dims, n_samples)


def normalize_data(data, bounds):
    """
    Normalize data to [0,1] based on bounds
    
    Args:
        data: Input data
        bounds: (lower_bound, upper_bound)
    
    Returns:
        Normalized data
    """
    lower, upper = bounds
    return (data - lower) / (upper - lower)


def denormalize_data(data, bounds):
    """
    Denormalize data from [0,1] to original bounds
    
    Args:
        data: Normalized data
        bounds: (lower_bound, upper_bound)
    
    Returns:
        Denormalized data
    """
    lower, upper = bounds
    return data * (upper - lower) + lower
