"""
并行化的IAF-FBO实验主脚本
支持多进程和多线程并行加速
"""

import numpy as np
import time
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from core import IAF_FBO
from utils import save_results, save_run_csv, print_final_statistics


def run_single_experiment(args):
    """运行单个实验（用于多进程并行）"""
    (run_id, client_ids, dimension, n_initial, max_fe, ucb_flag, 
     n_clusters, privacy_noise, transfer_flag, pop_size, max_iter, phi, n_jobs_per_run) = args
    
    print(f"开始运行实验 {run_id}")
    start_time = time.time()
    
    # 设置随机种子确保可重现性
    np.random.seed(int(time.time() * 1000 + run_id) % (2**32 - 1))
    
    # 初始化IAF-FBO算法
    iaf_fbo = IAF_FBO(
        client_ids=client_ids,
        dimension=dimension,
        n_initial=n_initial,
        max_fe=max_fe,
        ucb_flag=ucb_flag,
        n_clusters=n_clusters,
        privacy_noise=privacy_noise,
        transfer_flag=transfer_flag,
        pop_size=pop_size,
        max_iter=max_iter,
        phi=phi,
        n_jobs=n_jobs_per_run  # 每个实验内部的并行度
    )
    
    # 运行优化
    results = iaf_fbo.run(n_runs=1, verbose=False)
    
    end_time = time.time()
    run_time = end_time - start_time
    
    # 构建结果
    run_result = {
        'run_id': run_id,
        'results': results[0],
        'runtime': run_time,
        'evaluation_history': iaf_fbo.evaluation_history,
        'parameters': {
            'dimension': dimension,
            'n_initial': n_initial,
            'max_fe': max_fe,
            'ucb_flag': ucb_flag,
            'n_clusters': n_clusters,
            'privacy_noise': privacy_noise,
            'transfer_flag': transfer_flag
        }
    }
    
    print(f"实验 {run_id} 完成，用时: {run_time:.2f} 秒")
    return run_result


def main_parallel_experiment(n_runs=1, n_jobs_experiments=None, n_jobs_per_run=None):
    """
    并行化的主实验函数
    
    Args:
        n_runs: 实验运行次数
        n_jobs_experiments: 实验间并行度（None=自动检测）
        n_jobs_per_run: 每个实验内部并行度（None=自动检测）
    """
    print("IAF-FBO: 联邦贝叶斯多任务优化 (并行版本)")
    print("Python实现")
    print("=" * 60)
    
    # 自动设置并行度
    total_cores = mp.cpu_count()
    if n_jobs_experiments is None:
        n_jobs_experiments = min(n_runs, max(1, total_cores // 2))
    if n_jobs_per_run is None:
        n_jobs_per_run = max(1, total_cores // n_jobs_experiments)
    
    print(f"系统CPU核心数: {total_cores}")
    print(f"实验间并行度: {n_jobs_experiments}")
    print(f"每个实验内部并行度: {n_jobs_per_run}")
    print("-" * 40)
    
    # 实验参数（与用户要求一致）
    client_ids = list(range(1, 19))  # 18个客户端（任务）
    dimension = 10  # 用户指定10维
    n_initial = 50  # 初始样本数
    max_fe = 60  # 额外函数评估次数（50+60=110总计）
    ucb_flag = 2  # LCB获取函数
    n_clusters = 6
    privacy_noise = 0.0  # 基础实验无隐私噪声
    transfer_flag = True
    
    # CSO参数
    pop_size = 100
    max_iter = 100
    phi = 0.1
    
    all_results = []
    
    if n_runs == 1 or n_jobs_experiments == 1:
        # 单个实验或顺序执行
        for run in range(n_runs):
            args = (run + 1, client_ids, dimension, n_initial, max_fe, ucb_flag,
                   n_clusters, privacy_noise, transfer_flag, pop_size, max_iter, phi, n_jobs_per_run)
            result = run_single_experiment(args)
            all_results.append(result)
            
            # 保存单轮结果
            save_run_csv(result, dimension, max_fe, ucb_flag, n_clusters, transfer_flag)
    else:
        # 多实验并行执行
        print(f"并行运行 {n_runs} 个实验...")
        
        # 准备参数
        args_list = []
        for run in range(n_runs):
            args = (run + 1, client_ids, dimension, n_initial, max_fe, ucb_flag,
                   n_clusters, privacy_noise, transfer_flag, pop_size, max_iter, phi, n_jobs_per_run)
            args_list.append(args)
        
        # 并行执行实验
        with ProcessPoolExecutor(max_workers=n_jobs_experiments) as executor:
            all_results = list(executor.map(run_single_experiment, args_list))
        
        # 保存每轮结果
        for result in all_results:
            save_run_csv(result, dimension, max_fe, ucb_flag, n_clusters, transfer_flag)
    
    # 保存总体结果
    save_results(all_results, dimension, max_fe, ucb_flag, n_clusters, transfer_flag)
    
    # 打印最终统计
    print_final_statistics(all_results)
    
    return all_results


def quick_test_parallel():
    """快速并行测试"""
    print("运行快速并行测试...")
    
    iaf_fbo = IAF_FBO(
        client_ids=[1, 2, 3, 4],
        dimension=10,
        n_initial=20,
        max_fe=10,
        ucb_flag=2,
        n_clusters=2,
        pop_size=30,
        max_iter=20,
        n_jobs=4  # 使用4个并行任务
    )
    
    start_time = time.time()
    results = iaf_fbo.run(n_runs=1, verbose=True)
    end_time = time.time()
    
    print(f"并行测试完成，用时: {end_time - start_time:.2f} 秒")
    
    # 打印结果
    for client_id, result in results[0].items():
        print(f"客户端 {client_id}: 最优值 = {result['best_y']:.6f}")


def compare_sequential_vs_parallel():
    """比较顺序执行与并行执行的性能"""
    print("性能比较: 顺序 vs 并行")
    print("=" * 40)
    
    # 测试配置
    client_ids = [1, 2, 3, 4, 5, 6]
    dimension = 10
    n_initial = 20
    max_fe = 15
    
    # 顺序执行
    print("顺序执行...")
    start_time = time.time()
    iaf_fbo_seq = IAF_FBO(
        client_ids=client_ids,
        dimension=dimension,
        n_initial=n_initial,
        max_fe=max_fe,
        ucb_flag=2,
        n_clusters=3,
        pop_size=50,
        max_iter=30,
        n_jobs=1  # 顺序执行
    )
    results_seq = iaf_fbo_seq.run(n_runs=1, verbose=False)
    seq_time = time.time() - start_time
    
    # 并行执行
    print("并行执行...")
    start_time = time.time()
    iaf_fbo_par = IAF_FBO(
        client_ids=client_ids,
        dimension=dimension,
        n_initial=n_initial,
        max_fe=max_fe,
        ucb_flag=2,
        n_clusters=3,
        pop_size=50,
        max_iter=30,
        n_jobs=None  # 自动并行
    )
    results_par = iaf_fbo_par.run(n_runs=1, verbose=False)
    par_time = time.time() - start_time
    
    # 比较结果
    print(f"\n性能比较结果:")
    print(f"顺序执行时间: {seq_time:.2f} 秒")
    print(f"并行执行时间: {par_time:.2f} 秒")
    print(f"加速比: {seq_time/par_time:.2f}x")
    
    # 验证结果一致性
    avg_seq = np.mean([results_seq[0][cid]['best_y'] for cid in client_ids])
    avg_par = np.mean([results_par[0][cid]['best_y'] for cid in client_ids])
    print(f"顺序执行平均最优值: {avg_seq:.6f}")
    print(f"并行执行平均最优值: {avg_par:.6f}")


if __name__ == "__main__":
    # 选择实验类型
    experiment_type = input("选择实验类型:\n"
                          "1. 主实验 (完整)\n"
                          "2. 快速并行测试\n"
                          "3. 性能比较 (顺序 vs 并行)\n"
                          "4. 多轮并行实验\n"
                          "输入选择 (1-4): ").strip()
    
    if experiment_type == "1":
        results = main_parallel_experiment(n_runs=1)
    elif experiment_type == "2":
        quick_test_parallel()
    elif experiment_type == "3":
        compare_sequential_vs_parallel()
    elif experiment_type == "4":
        n_runs = int(input("输入实验轮数: "))
        n_jobs_exp = input("实验间并行度 (回车自动): ").strip()
        n_jobs_per = input("每个实验内部并行度 (回车自动): ").strip()
        
        n_jobs_exp = int(n_jobs_exp) if n_jobs_exp else None
        n_jobs_per = int(n_jobs_per) if n_jobs_per else None
        
        results = main_parallel_experiment(n_runs, n_jobs_exp, n_jobs_per)
    else:
        print("无效选择，运行快速测试...")
        quick_test_parallel()
