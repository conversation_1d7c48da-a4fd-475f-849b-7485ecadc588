"""
Test functions for optimization problems
"""

import numpy as np
import scipy.io as sio
import os


class TestFunction:
    """Base class for test functions"""
    
    def __init__(self, name, bounds, rotation_matrix=None, optimal_point=None):
        self.name = name
        self.bounds = bounds  # (lower, upper)
        self.rotation_matrix = rotation_matrix
        self.optimal_point = optimal_point
    
    def __call__(self, x):
        """Evaluate function at point(s) x"""
        raise NotImplementedError
    
    def transform_input(self, x):
        """Apply rotation and shift transformation"""
        if self.rotation_matrix is not None and self.optimal_point is not None:
            if x.ndim == 1:
                x = x.reshape(1, -1)
            # Ensure optimal_point has correct shape
            opt_point = np.atleast_1d(self.optimal_point)
            if len(opt_point) != x.shape[1]:
                opt_point = np.zeros(x.shape[1])
            # Apply transformation: M * (x - opt)'
            transformed = np.dot(self.rotation_matrix, (x - opt_point).T).T
            return transformed
        return x


class G<PERSON>wank(TestFunction):
    """Griewank function"""
    
    def __call__(self, x):
        x = np.atleast_2d(x)
        x_transformed = self.transform_input(x)
        
        results = []
        for xi in x_transformed:
            dim = len(xi)
            sum_sq = np.sum(xi**2)
            prod_cos = np.prod(np.cos(xi / np.sqrt(np.arange(1, dim+1))))
            obj = sum_sq / 4000 - prod_cos + 1
            results.append(obj)
        
        return np.array(results)


class Rastrigin(TestFunction):
    """Rastrigin function"""
    
    def __call__(self, x):
        x = np.atleast_2d(x)
        x_transformed = self.transform_input(x)
        
        results = []
        for xi in x_transformed:
            dim = len(xi)
            obj = 10 * dim + np.sum(xi**2 - 10 * np.cos(2 * np.pi * xi))
            results.append(obj)
        
        return np.array(results)


class Ackley(TestFunction):
    """Ackley function"""
    
    def __call__(self, x):
        x = np.atleast_2d(x)
        x_transformed = self.transform_input(x)
        
        results = []
        for xi in x_transformed:
            dim = len(xi)
            sum1 = np.sum(xi**2)
            sum2 = np.sum(np.cos(2 * np.pi * xi))
            avg_sum1 = sum1 / dim
            avg_sum2 = sum2 / dim
            
            obj = -20 * np.exp(-0.2 * np.sqrt(avg_sum1)) - np.exp(avg_sum2) + 20 + np.e
            results.append(obj)
        
        return np.array(results)


class Schwefel(TestFunction):
    """Schwefel function"""
    
    def __call__(self, x):
        x = np.atleast_2d(x)
        x_transformed = self.transform_input(x)
        
        results = []
        for xi in x_transformed:
            dim = len(xi)
            sum_val = np.sum(xi * np.sin(np.sqrt(np.abs(xi))))
            obj = 418.9829 * dim - sum_val
            results.append(obj)
        
        return np.array(results)


class Sphere(TestFunction):
    """Sphere function"""
    
    def __call__(self, x):
        x = np.atleast_2d(x)
        x_transformed = self.transform_input(x)
        
        results = []
        for xi in x_transformed:
            obj = np.sum(xi**2)
            results.append(obj)
        
        return np.array(results)


class Rosenbrock(TestFunction):
    """Rosenbrock function"""
    
    def __call__(self, x):
        x = np.atleast_2d(x)
        x_transformed = self.transform_input(x)
        
        results = []
        for xi in x_transformed:
            obj = np.sum(100 * (xi[1:] - xi[:-1]**2)**2 + (1 - xi[:-1])**2)
            results.append(obj)
        
        return np.array(results)


class Weierstrass(TestFunction):
    """Weierstrass function"""
    
    def __call__(self, x):
        x = np.atleast_2d(x)
        x_transformed = self.transform_input(x)
        
        results = []
        a = 0.5
        b = 3
        kmax = 20
        
        for xi in x_transformed:
            dim = len(xi)
            obj = 0
            
            for i in range(dim):
                for k in range(kmax + 1):
                    obj += (a**k) * np.cos(2 * np.pi * (b**k) * (xi[i] + 0.5))
            
            # Subtract constant term
            c = 0
            for k in range(kmax + 1):
                c += (a**k) * np.cos(2 * np.pi * (b**k) * 0.5)
            
            obj -= dim * c
            results.append(obj)
        
        return np.array(results)


def choose_problem(problem_id, dimension, data_dir="../50d"):
    """
    Choose optimization problem based on ID

    Args:
        problem_id: Problem identifier (1-18)
        dimension: Problem dimension
        data_dir: Directory containing .mat files

    Returns:
        function, bounds, rotation_matrix, optimal_point
    """

    def load_problem_data(mat_file, task_name, dimension):
        """Helper function to load problem data with dimension checking"""
        # For testing, always use default values
        return np.eye(dimension), np.zeros(dimension)

    # Default values
    rotation_matrix = None
    optimal_point = None
    
    if problem_id == 1:
        # CI_HS - Griewank
        rotation_matrix, optimal_point = load_problem_data('CI_H.mat', 'Task1', dimension)
        func = Griewank('Griewank', (-100, 100), rotation_matrix, optimal_point)
        bounds = (-100, 100)
        
    elif problem_id == 2:
        # Rastrigin
        rotation_matrix, optimal_point = load_problem_data('CI_H.mat', 'Task2', dimension)
        func = Rastrigin('Rastrigin', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 3:
        # CI_MS - Ackley
        rotation_matrix, optimal_point = load_problem_data('CI_M.mat', 'Task1', dimension)
        func = Ackley('Ackley', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 4:
        # Rastrigin
        try:
            data = sio.loadmat(os.path.join(data_dir, 'CI_M.mat'))
            rotation_matrix = data['Rotation_Task2'][:dimension, :dimension]
            optimal_point = data['GO_Task2'][:dimension].flatten()
        except:
            rotation_matrix = np.eye(dimension)
            optimal_point = np.zeros(dimension)
        
        func = Rastrigin('Rastrigin', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 5:
        # CI_LS - Ackley
        try:
            data = sio.loadmat(os.path.join(data_dir, 'CI_L.mat'))
            rotation_matrix = data['Rotation_Task1'][:dimension, :dimension]
            optimal_point = data['GO_Task1'][:dimension].flatten()
        except:
            rotation_matrix = np.eye(dimension)
            optimal_point = np.zeros(dimension)
        
        func = Ackley('Ackley', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 6:
        # Schwefel
        rotation_matrix = np.eye(dimension)
        optimal_point = np.zeros(dimension)
        func = Schwefel('Schwefel', (-500, 500), rotation_matrix, optimal_point)
        bounds = (-500, 500)
        
    elif problem_id == 7:
        # PI_HS - Rastrigin
        try:
            data = sio.loadmat(os.path.join(data_dir, 'PI_H.mat'))
            rotation_matrix = data['Rotation_Task1'][:dimension, :dimension]
            optimal_point = data['GO_Task1'][:dimension].flatten()
        except:
            rotation_matrix = np.eye(dimension)
            optimal_point = np.zeros(dimension)
        
        func = Rastrigin('Rastrigin', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 8:
        # Sphere
        try:
            data = sio.loadmat(os.path.join(data_dir, 'PI_H.mat'))
            optimal_point = data['GO_Task2'][:dimension].flatten()
        except:
            optimal_point = np.zeros(dimension)
        
        rotation_matrix = np.eye(dimension)
        func = Sphere('Sphere', (-100, 100), rotation_matrix, optimal_point)
        bounds = (-100, 100)
        
    elif problem_id == 9:
        # PI_MS - Ackley
        try:
            data = sio.loadmat(os.path.join(data_dir, 'PI_M.mat'))
            rotation_matrix = data['Rotation_Task1'][:dimension, :dimension]
            optimal_point = data['GO_Task1'][:dimension].flatten()
        except:
            rotation_matrix = np.eye(dimension)
            optimal_point = np.zeros(dimension)
        
        func = Ackley('Ackley', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 10:
        # Rosenbrock
        rotation_matrix = np.eye(dimension)
        optimal_point = np.zeros(dimension)
        func = Rosenbrock('Rosenbrock', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 11:
        # PI_LS - Ackley
        try:
            data = sio.loadmat(os.path.join(data_dir, 'PI_L.mat'))
            rotation_matrix = data['Rotation_Task1'][:dimension, :dimension]
            optimal_point = data['GO_Task1'][:dimension].flatten()
        except:
            rotation_matrix = np.eye(dimension)
            optimal_point = np.zeros(dimension)
        
        func = Ackley('Ackley', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 12:
        # Weierstrass
        rotation_matrix = np.eye(dimension)
        optimal_point = np.zeros(dimension)
        func = Weierstrass('Weierstrass', (-0.5, 0.5), rotation_matrix, optimal_point)
        bounds = (-0.5, 0.5)
        
    elif problem_id == 13:
        # NI_HS - Rosenbrock
        rotation_matrix = np.eye(dimension)
        optimal_point = np.zeros(dimension)
        func = Rosenbrock('Rosenbrock', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 14:
        # Rastrigin
        try:
            data = sio.loadmat(os.path.join(data_dir, 'NI_H.mat'))
            rotation_matrix = data['Rotation_Task2'][:dimension, :dimension]
            optimal_point = data['GO_Task2'][:dimension].flatten()
        except:
            rotation_matrix = np.eye(dimension)
            optimal_point = np.zeros(dimension)
        
        func = Rastrigin('Rastrigin', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 15:
        # NI_MS - Griewank
        try:
            data = sio.loadmat(os.path.join(data_dir, 'NI_M.mat'))
            rotation_matrix = data['Rotation_Task1'][:dimension, :dimension]
            optimal_point = data['GO_Task1'][:dimension].flatten()
        except:
            rotation_matrix = np.eye(dimension)
            optimal_point = np.zeros(dimension)
        
        func = Griewank('Griewank', (-100, 100), rotation_matrix, optimal_point)
        bounds = (-100, 100)
        
    elif problem_id == 16:
        # Weierstrass
        try:
            data = sio.loadmat(os.path.join(data_dir, 'NI_M.mat'))
            rotation_matrix = data['Rotation_Task2'][:dimension, :dimension]
            optimal_point = data['GO_Task2'][:dimension].flatten()
        except:
            rotation_matrix = np.eye(dimension)
            optimal_point = np.zeros(dimension)
        
        func = Weierstrass('Weierstrass', (-0.5, 0.5), rotation_matrix, optimal_point)
        bounds = (-0.5, 0.5)
        
    elif problem_id == 17:
        # NI_LS - Rastrigin
        try:
            data = sio.loadmat(os.path.join(data_dir, 'NI_L.mat'))
            rotation_matrix = data['Rotation_Task1'][:dimension, :dimension]
            optimal_point = data['GO_Task1'][:dimension].flatten()
        except:
            rotation_matrix = np.eye(dimension)
            optimal_point = np.zeros(dimension)
        
        func = Rastrigin('Rastrigin', (-50, 50), rotation_matrix, optimal_point)
        bounds = (-50, 50)
        
    elif problem_id == 18:
        # Schwefel
        rotation_matrix = np.eye(dimension)
        optimal_point = np.zeros(dimension)
        func = Schwefel('Schwefel', (-500, 500), rotation_matrix, optimal_point)
        bounds = (-500, 500)
        
    else:
        # Default case - use Sphere function
        rotation_matrix = np.eye(dimension)
        optimal_point = np.zeros(dimension)
        func = Sphere('Sphere', (-100, 100), rotation_matrix, optimal_point)
        bounds = (-100, 100)
    
    return func, bounds, rotation_matrix, optimal_point
