# IAF-FBO: Implicit Acquisition Function - Federated Bayesian Optimization

Python implementation of the algorithm described in "Optimization of an Implicit Acquisition Function for Federated Bayesian Many-Task Optimization".

## Overview

IAF-FBO is a federated Bayesian optimization algorithm that enables multiple clients to collaboratively optimize their individual tasks while preserving data privacy. The algorithm uses neural network classifiers to learn pairwise relationships between solutions and employs competitive swarm optimization to optimize an implicit global acquisition function.

## Key Features

- **Privacy-Preserving**: Only model parameters are shared, not raw data
- **Multi-Task Optimization**: Supports optimization of different tasks across clients
- **Knowledge Transfer**: Enables knowledge sharing between similar tasks
- **Flexible Acquisition Functions**: Supports UCB, LCB, and EI acquisition functions
- **Scalable**: Can handle multiple clients and high-dimensional problems

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure you have the test function data files in the `../50d/` directory (CI_H.mat, CI_M.mat, etc.)

## Quick Start

### Simple Test
```python
from core import IAF_FBO

# Initialize with simple configuration
iaf_fbo = IAF_FBO(
    client_ids=[1, 2, 3],      # 3 clients
    dimension=10,               # 10D problems
    n_initial=20,              # 20 initial samples
    max_fe=15,                 # 15 optimization iterations
    ucb_flag=2,                # LCB acquisition function
    n_clusters=2               # 2 clusters for grouping
)

# Run optimization
results = iaf_fbo.run(n_runs=1, verbose=True)

# Print results
for client_id, result in results[0].items():
    print(f"Client {client_id}: Best = {result['best_y']:.6f}")
```

### Full Experiment
```python
# Run the main experiment (similar to MATLAB script)
python main_iaf_fbo.py
```

## Algorithm Parameters

### Core Parameters
- `client_ids`: List of client IDs (default: 1-18)
- `dimension`: Problem dimension (default: 50)
- `n_initial`: Number of initial samples (default: 50)
- `max_fe`: Maximum function evaluations per client (default: 60)

### Acquisition Function
- `ucb_flag`: Acquisition function type
  - 0: Expected Improvement (EI)
  - 1: Upper Confidence Bound (UCB)
  - 2: Lower Confidence Bound (LCB)

### Federated Learning
- `n_clusters`: Number of clusters for client grouping (default: 6)
- `transfer_flag`: Enable knowledge transfer (default: True)
- `privacy_noise`: Privacy noise level (default: 0.0)

### Optimization
- `pop_size`: Population size for CSO (default: 100)
- `max_iter`: Maximum iterations for CSO (default: 100)
- `phi`: Learning factor for CSO (default: 0.1)

## Test Functions

The algorithm supports 18 different optimization problems:

1. **CI_HS**: Griewank (rotated)
2. **CI_HS**: Rastrigin (rotated)
3. **CI_MS**: Ackley (rotated)
4. **CI_MS**: Rastrigin (rotated)
5. **CI_LS**: Ackley (rotated)
6. **Schwefel**
7. **PI_HS**: Rastrigin (rotated)
8. **PI_HS**: Sphere (shifted)
9. **PI_MS**: Ackley (rotated)
10. **Rosenbrock**
11. **PI_LS**: Ackley (rotated)
12. **Weierstrass**
13. **NI_HS**: Rosenbrock
14. **NI_HS**: Rastrigin (rotated)
15. **NI_MS**: Griewank (rotated)
16. **NI_MS**: Weierstrass (rotated)
17. **NI_LS**: Rastrigin (rotated)
18. **Schwefel**

## File Structure

```
pyIAF/
├── __init__.py              # Package initialization
├── core.py                  # Main IAF-FBO algorithm
├── gaussian_process.py      # Gaussian Process implementation
├── neural_classifier.py     # Neural network classifier
├── cso_optimizer.py         # Competitive Swarm Optimizer
├── test_functions.py        # Optimization test functions
├── utils.py                 # Utility functions
├── main_iaf_fbo.py         # Main experiment script
├── test_iaf_fbo.py         # Test script
├── requirements.txt         # Dependencies
└── README.md               # This file
```

## Usage Examples

### 1. Compare Acquisition Functions
```python
from test_iaf_fbo import compare_acquisition_functions
results = compare_acquisition_functions()
```

### 2. Test Privacy Noise Effect
```python
from test_iaf_fbo import test_privacy_noise
results = test_privacy_noise()
```

### 3. Custom Configuration
```python
iaf_fbo = IAF_FBO(
    client_ids=[1, 3, 5, 7],   # Custom client selection
    dimension=30,
    ucb_flag=1,                # UCB acquisition function
    privacy_noise=0.1,         # Add privacy noise
    transfer_flag=False        # Disable knowledge transfer
)
results = iaf_fbo.run(n_runs=3)
```

## Algorithm Workflow

1. **Initialization**: Each client generates initial samples using Latin Hypercube Sampling
2. **Local GP Training**: Each client trains a Gaussian Process on local data
3. **Classifier Training**: Each client creates pairwise comparison data and trains a neural classifier
4. **Client Clustering**: Clients are grouped based on classifier similarity using K-means
5. **Model Aggregation**: Classifier parameters are aggregated within each cluster
6. **Global Optimization**: Competitive Swarm Optimizer optimizes the implicit acquisition function
7. **Solution Evaluation**: New solutions are evaluated and added to local datasets
8. **Iteration**: Steps 2-7 are repeated until convergence

## Performance Notes

- The algorithm is computationally intensive due to neural network training and CSO optimization
- For large-scale experiments, consider using GPU acceleration for neural network training
- Memory usage scales with the number of clients and problem dimension
- Runtime is approximately O(clients × max_fe × (GP_training + NN_training + CSO_optimization))

## Differences from MATLAB Implementation

1. **Gaussian Process**: Uses scikit-learn GP instead of DACE toolbox
2. **Neural Networks**: Uses PyTorch instead of MATLAB Neural Network Toolbox
3. **Optimization**: CSO implementation adapted for Python
4. **Data Handling**: Uses NumPy arrays instead of MATLAB matrices
5. **Clustering**: Uses scikit-learn K-means instead of MATLAB kmeans

## Citation

If you use this code, please cite the original paper:
```
@article{liu2025optimization,
  title={Optimization of an Implicit Acquisition Function for Federated Bayesian Many-Task Optimization},
  author={Liu, Qiqi and Jin, Yaochu and Chen, Guodong},
  journal={IEEE Transactions on Evolutionary Computation},
  year={2025}
}
```

## License

This implementation is provided for research purposes. Please refer to the original paper for licensing terms.
